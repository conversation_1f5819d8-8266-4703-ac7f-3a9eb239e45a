#!/usr/bin/env python3
"""
测试脚本：验证Python路径设置是否正确
用于在IDE中测试模块导入是否正常工作
"""

import sys
from pathlib import Path

def test_path_setup():
    """测试路径设置和模块导入"""
    print("=== Python路径设置测试 ===")
    
    # 显示当前工作目录
    print(f"当前工作目录: {Path.cwd()}")
    
    # 显示当前文件路径
    current_file = Path(__file__).resolve()
    print(f"当前文件路径: {current_file}")
    
    # 模拟webhook_server.py中的路径设置逻辑
    if getattr(sys, 'frozen', False):
        src_path = str(Path(sys._MEIPASS))
    else:
        # 从test_path_setup.py推断到src目录
        src_path = str(current_file.parent / "src")
        
        # 验证路径是否正确（检查是否存在common目录）
        if not (Path(src_path) / "common").exists():
            print(f"警告: 推断的src路径不正确: {src_path}")
            # 如果推断的路径不正确，尝试从当前工作目录查找
            cwd = Path.cwd()
            potential_src = cwd / "src"
            if potential_src.exists() and (potential_src / "common").exists():
                src_path = str(potential_src)
                print(f"使用当前工作目录下的src: {src_path}")
            else:
                # 最后尝试：向上查找包含src目录的父目录
                current_dir = current_file.parent
                while current_dir.parent != current_dir:
                    if (current_dir / "src" / "common").exists():
                        src_path = str(current_dir / "src")
                        print(f"通过向上查找找到src: {src_path}")
                        break
                    current_dir = current_dir.parent
    
    print(f"解析的src路径: {src_path}")
    
    # 检查关键目录是否存在
    src_dir = Path(src_path)
    common_dir = src_dir / "common"
    webhook_server_dir = src_dir / "webhook_server"
    
    print(f"src目录存在: {src_dir.exists()}")
    print(f"common目录存在: {common_dir.exists()}")
    print(f"webhook_server目录存在: {webhook_server_dir.exists()}")
    
    # 添加到sys.path
    if src_path not in sys.path:
        sys.path.insert(0, src_path)
        print(f"已将 {src_path} 添加到 sys.path")
    else:
        print(f"{src_path} 已在 sys.path 中")
    
    print(f"sys.path前3项: {sys.path[:3]}")
    
    # 测试模块导入
    print("\n=== 模块导入测试 ===")
    try:
        from common.constants import common_constants
        print("✅ 成功导入 common.constants.common_constants")
    except ImportError as e:
        print(f"❌ 导入 common.constants.common_constants 失败: {e}")
    
    try:
        from webhook_server.config import constants
        print("✅ 成功导入 webhook_server.config.constants")
    except ImportError as e:
        print(f"❌ 导入 webhook_server.config.constants 失败: {e}")
    
    try:
        from webhook_server.models import webhook_server
        print("✅ 成功导入 webhook_server.models.webhook_server")
    except ImportError as e:
        print(f"❌ 导入 webhook_server.models.webhook_server 失败: {e}")

if __name__ == "__main__":
    test_path_setup()
