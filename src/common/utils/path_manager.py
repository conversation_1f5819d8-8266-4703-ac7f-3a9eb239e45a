"""统一的路径管理模块。

此模块是项目中唯一处理sys.path的地方，避免在多个文件中重复相同的路径处理逻辑。
其他文件只需要简单的from导入即可。
"""

import sys
from pathlib import Path


# 动态路径处理，支持打包后的环境
if getattr(sys, 'frozen', False):
    # 打包后的环境
    src_path = str(Path(sys._MEIPASS))
else:
    # 开发环境，从当前文件位置推断
    src_path = str(Path(__file__).parent.parent.parent)

# 确保源代码路径在sys.path中（只在这里处理一次）
if src_path not in sys.path:
    sys.path.insert(0, src_path)


def get_src_path() -> str:
    """获取源代码根路径的便捷函数。"""
    return src_path


def get_relative_path(*path_parts: str) -> str:
    """获取相对于源代码根目录的路径。
    
    Args:
        *path_parts: 路径组件
        
    Returns:
        str: 完整路径
    """
    return str(Path(src_path).joinpath(*path_parts))