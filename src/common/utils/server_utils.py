"""服务端相关的工具函数模块。

此模块包含了与服务端相关的各种工具函数，包括：
- 日志记录工具
- 文件和路径处理
- 网络和IP地址验证
- 配置文件处理
- 系统信息获取
- 进程管理
"""

import asyncio
import configparser
import hashlib
import inspect
import logging
import os
import socket
import subprocess
import sys
import threading
import time
import traceback
from datetime import datetime, timedelta
from ipaddress import ip_address, ip_network
from typing import (
    Any, Awaitable, Callable, Dict, List, Literal, Optional, Set, Tuple, Union
)
from zoneinfo import ZoneInfo, ZoneInfoNotFoundError

import chardet
import psutil

from common.constants import common_constants
from common.utils import self_log, shutdown_exec_funct

logger = logging.getLogger(__name__)
# 全局注册表，保存每个 func 对应的执行状态和锁
_run_once_locks: dict[Callable[..., Any], threading.Lock] = {}
_run_once_flags: dict[Callable[..., Any], bool] = {}
_run_once_global_lock = threading.Lock()

# 获取大于start_time时间的符合target_time的 datetime 对象（包含年月日时分秒）;过期的不要
def get_next_datetime(start_time: datetime, target_time: datetime.time) -> datetime:
    # 获取和启动时间相同的date的 target_time 时间
    date_start_time = start_time.date()
    date_target = datetime.combine(date_start_time, target_time, tzinfo=start_time.tzinfo)
    if start_time < date_target:
        return date_target

    # 构造明天的 target_time 时间
    tomorrow_date = date_start_time + timedelta(days=1)
    return datetime.combine(tomorrow_date, target_time, tzinfo=start_time.tzinfo)


# 判断IP是否在白名单中
def is_ip_in_whitelist(client_ip: str, whitelist: list[str]) -> bool:
    logger_print(f"checking if ip {client_ip} is in whitelist: {whitelist}", custom_logger=logger)
    try:
        parsed_client_ip = ip_address(client_ip)
        logger_print(f"parsed client ip: {parsed_client_ip}, version: {parsed_client_ip.version}", custom_logger=logger)

        for i, whitelist_ip in enumerate(whitelist):
            logger_print(f"checking against whitelist entry {i+1}: {whitelist_ip}", custom_logger=logger)
            network = ip_network(whitelist_ip, strict=False)
            logger_print(f"parsed network: {network}, version: {network.version}", custom_logger=logger)

            if parsed_client_ip.version == network.version and parsed_client_ip in network:
                logger_print(f"ip {client_ip} matches whitelist entry {whitelist_ip}", custom_logger=logger)
                return True

        logger_print(f"ip {client_ip} does not match any whitelist entries", custom_logger=logger)
        return False
    except ValueError:
        logger_print(f"invalid ip address: {client_ip}!", custom_logger=logger,  use_exception=True)
        return False


# 转换存储单位为字节数,storage_str 如 "1.25MB"
def convert_storage_str_to_bytes(storage_str: str) -> float:
    storage_str = trim(storage_str)
    if storage_str is None:
        raise ValueError("storage string can not be empty!")
    storage_str = storage_str.replace(" ", "")
    str_match = common_constants.STORAGE_UNIT_PATTERN.fullmatch(storage_str)
    if not str_match:
        raise ValueError(f"invalid storage string: {storage_str}")
    num, unit = str_match.groups()
    num = float(num)
    unit = unit.upper()
    try:
        multiplier = common_constants.STORAGE_UNIT[unit]
    except KeyError:
        raise ValueError(f"unknown unit: {unit!r} in {storage_str!r}")

    return float(num * multiplier)


# 该函数用于保证函数只执行一次且线程安全[使用该函数越多,对应的func也需要锁和状态描述,造成内存会越来越大:绑定在类实例上]
def run_once(func: Callable[..., Any], *args, **kwargs) -> Any:
    func_name = getattr(func, '__name__', str(func))
    logger_print(f"run_once called for function: {func_name}", custom_logger=logger)

    with _run_once_global_lock:
        if func not in _run_once_locks:
            logger_print(f"initializing lock and flag for function: {func_name}", custom_logger=logger)
            _run_once_locks[func] = threading.Lock()
            _run_once_flags[func] = False
        else:
            logger_print(f"using existing lock for function: {func_name}", custom_logger=logger)

    lock = _run_once_locks[func]
    with lock:
        if not _run_once_flags[func]:
            logger_print(f"executing function {func_name} for the first time", custom_logger=logger)
            _run_once_flags[func] = True
            result = func(*args, **kwargs)
            logger_print(f"function {func_name} executed successfully", custom_logger=logger)
            return result
        else:
            logger_print(f"function {func_name} already executed, skipping", custom_logger=logger)


# 该函数用于将字符串中前后空格去除,为空则返回None
def trim(str_value: str | None) -> str | None:
    if str_value is None:
        return None
    if not isinstance(str_value, str):
        logger_print(f"current value is not string, current value is {str_value}, type is {type(str_value)}", custom_logger=logger, log_level=logging.WARNING)
        return None
    strip_value = str_value.strip()
    return strip_value or None


# 配置项key对应的值必须可以转换成整数
def tran_int(key: str, value: str) -> int:
    try:
        return int(value)
    except ValueError as e:
        raise ValueError(f"config key:{key} - value:{value} must be a valid integer!") from e

# 该函数用于将配置文件中对应section节点内容并转换为字典
def section_to_dict(config: configparser.ConfigParser, section_name: str, allow_value_none: bool = False,allow_empty_section: bool = False) -> Dict[
    str, Any]:
    if section_name is None or not config.has_section(section_name):
        raise ValueError(f"current section :{section_name} not exist in config file!")
    config_file_section = config[section_name]
    res = {}
    for raw_key, raw_value in config_file_section.items():
        key = trim(raw_key)
        if key is None:
            raise ValueError(f"{section_name} section key can not be empty,current key is {raw_key}")
        if key in res:
            raise ValueError(f"{section_name} section key can not be duplicated,current key is {raw_key}")
        value = trim(raw_value)
        if value is None and not allow_value_none:
            raise ValueError(
                f"{section_name} section value can not be empty,current key is {raw_key},current value is {raw_value}")
        res[key] = value
    if not res and not allow_empty_section:
        raise ValueError(f"{section_name} section can not be empty!")
    return res

def get_real_path(path: str)->str:
    """获取指定文件路径的真实全路径"""
    if not path or not isinstance(path, str):
        raise ValueError(f"path must be a non-empty string,current value is {path}")
    return os.path.realpath(os.path.expanduser(path))
# 获取本地文件路径实际对应的全路径:如果文件不存在则抛出异常
def get_real_exist_file_path(path: str) -> str:
    try:
        real_path = get_real_path(path)
        if not os.path.isfile(real_path):
            raise ValueError(f"file not exist: {path}")
        return real_path
    except OSError as e:
        raise ValueError(f"invalid file path: {path}") from e

# 获取对应路径的真实路径,并创建目录:[如果path是目录,则创建目录 --- path_is_dir=True]
def get_real_path_create_dir(path: str,path_is_dir:bool=False) -> str:
    try:
        real_path = get_real_path(path)
        # make sure the directory exists
        dir_path = real_path if path_is_dir else os.path.dirname(real_path)
        os.makedirs(dir_path, exist_ok=True)
        return real_path
    except Exception as e:
        # 在PyInstaller环境下，如果路径创建失败，尝试使用临时目录
        import tempfile
        logger_print(f"failed to create directory for path {path}, using temp directory: {e}", custom_logger=logger, log_level=logging.WARNING)

        if path_is_dir:
            # 如果是目录，在临时目录下创建
            temp_dir = tempfile.mkdtemp(prefix="webhook_server_")
            return temp_dir
        else:
            # 如果是文件，在临时目录下创建文件路径
            temp_dir = tempfile.mkdtemp(prefix="webhook_server_")
            filename = os.path.basename(path)
            return os.path.join(temp_dir, filename)

def is_utf8_encoding_file(file_path, chunk_size=4096):
    """
    判断指定文件路径对应文件是否是UTF-8编码的文件
    前提:
    1. 文件必须存在
    2. 文件必须可读
    """
    file_path=os.path.realpath(os.path.expanduser(file_path))
    if not os.path.isfile(file_path) or not os.access(file_path, os.R_OK):
        return False
    try:
        with open(file_path, 'rb') as f:
            chunk = f.read(chunk_size)
            if not chunk:
                # 空文件被视为合法的UTF-8编码文件
                return True
            file_info=chardet.detect(chunk)
            encoding=file_info.get('encoding','').lower()
            confidence=file_info.get('confidence',0.0)
            logger_print(f"文件{file_path}的编码为{encoding},置信度为{confidence}", custom_logger=logger, log_level=logging.DEBUG)
            return encoding in ['utf-8','utf-8-sig','ascii'] and confidence>0.9
    except UnicodeDecodeError:
        return False
    except Exception: # noqa
        logger_print("检查编码时出错!", custom_logger=logger, use_exception=True)
        return False

def file_hash(file_path: str,
        algorithm: Literal[
            "md5", "sha1", "sha224", "sha256", "sha384", "sha512",
            "sha3_224", "sha3_256", "sha3_384", "sha3_512",
            "blake2b", "blake2s"
        ] = "sha256",
        chunk_size: int = 65536
) -> str:
    """
    计算文件的哈希值。

    Args:
        file_path: 要读取并计算哈希值的文件路径。
        algorithm: 哈希算法名称，默认 'sha256'。可选 'md5', 'sha1',
                   'sha224', 'sha384', 'sha512', 'sha3_224', 'sha3_256',
                   'sha3_384', 'sha3_512', 'blake2b', 'blake2s' 等。
        chunk_size: 每次读取的字节数，默认 8192。

    Returns:
        文件的十六进制哈希字符串。
    """
    if chunk_size <= 0:
        raise ValueError("chunk_size must be greater than 0!")
    if not os.path.isfile(file_path):
        raise ValueError(f"文件{file_path}实际不存在,无法计算哈希值!")
    # 创建对应算法的哈希对象
    try:
        hasher = hashlib.new(algorithm)
    except ValueError:
        raise ValueError(f"不支持的哈希算法: {algorithm}")

    # 分块读取文件并更新哈希
    try:
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(chunk_size), b""):
                hasher.update(chunk)
            return hasher.hexdigest()
    except OSError:
        raise ValueError(f"文件{file_path}无法读取,无法计算哈希值!")

# 该函数用于将字符串转换为布尔值,如果字符串为空则返回None
def str_to_bool(s: str) -> bool | None:
    s = trim(s)
    if s is None:
        return None
    if s.lower() == 'true':
        return True
    elif s.lower() == 'false':
        return False
    else:
        raise ValueError(f"invalid bool string: {s},the string must be 'true' or 'false'")

def check_time_zone_str(time_zone_str: str):
    try:
        ZoneInfo(time_zone_str)
    except ZoneInfoNotFoundError:
        raise ValueError(
            f"server properties [server]-[time_zone] value must be a valid timezone,current value is {time_zone_str}")

def check_lan_host(lan_host: str):
    # 必须是局域网IP
    try:
        host_ip = ip_address(lan_host)
    except ValueError:
        raise ValueError("server properties [server]-[host] value must be a valid IP address!")
    if not host_ip.is_private:
        raise ValueError("server properties [server]-[host] value must be a private IP address!")


# 该函数用于获取服务端配置的端口号
def get_server_config_port(port_str: str) -> int:
    port_int = tran_int("port", port_str)
    if not (common_constants.MIN_PORT <= port_int <= common_constants.MAX_PORT):
        raise ValueError(
            f"server properties [server]-[port] value must be between {common_constants.MIN_PORT} and {common_constants.MAX_PORT},current value is {port_str}")
    return port_int

# 将IP白名单对应的字符串转换为IP白名单列表:其中*表示允许所有IP访问
def get_whitelist(whitelist_config_value: str) -> List[str]:
    res_list:List[str] = []
    allowed_all = False
    for whitelist_ip in whitelist_config_value.split(","):
        # 校验字符串必须是IP或者网段
        new_whitelist_ip = trim(whitelist_ip)
        if new_whitelist_ip is None:
            continue
        # 不限制白名单
        if new_whitelist_ip == "*":
            allowed_all = True
            res_list.clear()
            res_list.append("*")
            continue
        # 校验IP或者网段是否合法
        try:
            ip_network(new_whitelist_ip, strict=False)
        except ValueError:
            raise ValueError(
                f"server properties [server]-[whitelist] value must be a valid IP or IP network,current value is {whitelist_ip}")
        if not allowed_all:
            res_list.append(new_whitelist_ip)
    if not res_list:
        raise ValueError("server properties [server]-[whitelist] value can not empty!")
    return res_list



def is_local_host(host: str)->bool:
    """判断host是否指代本机IP"""
    if not host or not isinstance(host, str):
        raise ValueError("host must be a non-empty string!")
    host=host.lower()
    if host in ["localhost", "127.0.0.1", "0.0.0.0","::1","0:0:0:0:0:0:0:1"]:
        return True
    try:
        host_ip = ip_address(host)
        return host_ip.is_loopback
    except ValueError:
        logger_print(f"invalid host: {host}",custom_logger=logger,use_exception=True)
        raise

def is_port_occupied(port: int, ip: str = "127.0.0.1") -> bool:
    """
    检查指定IP和端口是否被占用
    该函数用于确定特定IP地址和端口组合是否已被其他服务占用，
    通常用于在启动服务前检查端口可用性。
    :param port: 端口号
    :param ip: 要检查的IP地址 (默认127.0.0.1)
    :return: True表示占用，False表示空闲
    """
    for conn in psutil.net_connections(kind='inet'):
        # 检查TCP监听连接
        if conn.status == 'LISTEN' and conn.laddr:
            addr_ip, addr_port = conn.laddr
            # 检查端口和IP是否匹配（包括0.0.0.0的特殊情况） 0.0.0.0 的特殊检查必须包含在内
            if addr_port == port and (ip=='0.0.0.0' or addr_ip == '0.0.0.0' or addr_ip == ip):
                return True
    return False

def get_local_lan_ip(fallback: str = "127.0.0.1") -> str:
    """
    获取本机在局域网中的 IPv4 地址。

    原理：向一个外部地址（不一定可达）创建 UDP “连接”，
    OS 会自动选择一条出网的本地网卡和 IP，随后我们从 socket 获取到本地地址。

    :param fallback: 如果获取失败，返回的默认 IP（回环地址）。
    :return: 本机局域网 IPv4 地址字符串。
    """
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as s:
            # 这里的 IP 和端口不需要真的可达，只要是合法的 IPv4 格式即可
            s.connect(("*******", 80))
            local_ip = s.getsockname()[0]
    except Exception: # noqa
        local_ip = fallback
    return local_ip

TaskFuncType = Union[
    Callable[...,Any],
    Callable[..., Awaitable[Any]]
]

# 定时任务通用外部嵌套函数 --- 可以传递带有参数的同步/异步函数
async def run_safe_task(
        task_func: TaskFuncType,
        scheduler_check: Callable[[], bool],
        task_canceled_info: str,
        task_exception_info: str,
        custom_logger: Optional[logging.Logger] = None,
        *args,
        **kwargs
) -> None:
    try:
        if not scheduler_check():
            return

        if inspect.iscoroutinefunction(task_func):
            await task_func(*args, **kwargs)  # 异步函数
        else:
            task_func(*args, **kwargs)        # 同步函数
    except asyncio.CancelledError:
        logger_print(task_canceled_info,custom_logger=custom_logger)
    except BaseException as ex:
        logger_print(task_exception_info,custom_logger=custom_logger,use_exception=True,exception=ex)


# 打印日志信息 --- 在对于logger可能为空的情况下,兼容print打印日志信息
def logger_print(msg: str, custom_logger: Optional[logging.Logger],log_level:int=logging.INFO,use_exception:bool=False, exception: Optional[BaseException] = None):
    show_msg = msg if exception is None else f"{msg} \n {exception}"
    if  custom_logger is None or not self_log.log_config.had_init():
        print_tmp_file(msg,print_error=use_exception or exception or log_level>=logging.WARNING, exception=exception)
        if use_exception or exception:
            traceback.print_exc(file=sys.stderr)
        return
    
    if use_exception:
        custom_logger.exception(show_msg)
    else:
        custom_logger.log(log_level, show_msg)

def print_tmp_file(msg:str,print_error:bool=False, exception: Optional[BaseException] = None):
    """在没有logger的情况下,将日志信息打印到临时文件中"""
    show_msg = f"{msg}" if exception is None else f"{msg} \n {exception}"
    now=datetime.now(common_constants.DEFAULT_TIMEZONE)
    now_str=now.strftime(common_constants.DATETIME_FORMAT)
    show_msg=f"{now_str} {show_msg}"

    try:
        tmp_file_path = common_constants.TEMP_LOG_FILE_PATH +now.strftime("%Y-%m-%d")+".log"
        tmp_log_file=get_real_path_create_dir(tmp_file_path,path_is_dir=False)
        with open(tmp_log_file,"a",encoding="utf-8") as f:
            f.write(show_msg)
            f.write("\n")
    except Exception as file_error:
        # 如果文件写入失败，尝试安全地输出到stderr
        try:
            print(f"Failed to write to temp log file: {file_error}", file=sys.stderr)
        except (OSError, ValueError):
            # 如果stderr也不可用，则忽略
            pass

    # 安全地输出到标准流
    try:
        if exception or print_error:
            print(show_msg, file=sys.stderr)
        else:
            print(show_msg)
    except (OSError, ValueError):
        # 如果标准流不可用（如在exe环境下被重定向），则忽略
        # 这种情况下，至少日志文件中会有记录
        pass

def is_same_function(
    func1: Callable[..., Any],args1: Tuple[Any, ...],kwargs1: Dict[str, Any],
    func2: Callable[..., Any],args2: Tuple[Any, ...],kwargs2: Dict[str, Any],
) -> bool:
    """
    Compare two “function” (function + args/kwargs) for semantic equality.
    - Unwraps bound methods so that `obj.method` is tested by (function, instance).
    - Normalizes defaults via inspect.signature.bind_partial + apply_defaults.
    """

    def unwrap(f: Callable) -> Tuple[Callable, Optional[Any]]:
        """
        If f is a bound method, return (f.__func__, f.__self__),
        else (f, None).
        """
        # Unwrap decorator chain
        while hasattr(f, '__wrapped__'):
            f = f.__wrapped__
        if hasattr(f, "__self__") and hasattr(f, "__func__"):
            return f.__func__, f.__self__
        return f, None

    def normalize(
            f: Callable,
            args: Tuple[Any, ...],
            kwargs: Dict[str, Any],
    ) -> Dict[str, Any]:
        """
        Bind given args/kwargs to the signature of f, fill in defaults,
        and return the full argument mapping.
        """
        sig = inspect.signature(f)
        try:
            bound = sig.bind(*args, **kwargs)  # 改为全绑定方式
        except TypeError:
            bound = sig.bind_partial(*args, **kwargs)
        bound.apply_defaults()
        # 处理可变参数
        arguments = dict(bound.arguments)
        for param in sig.parameters.values():
            if param.kind == param.VAR_POSITIONAL:
                arguments[param.name] = tuple(arguments.get(param.name, []))
            elif param.kind == param.VAR_KEYWORD:
                arguments[param.name] = dict(arguments.get(param.name, {}))

        return arguments

    # 1) Unwrap bound methods
    real_func1, self1 = unwrap(func1)
    real_func2, self2 = unwrap(func2)
    if real_func1 is not real_func2 or self1 != self2:
        return False

    # 2) Normalize argument binding
    try:
        norm1 = normalize(real_func1, args1, kwargs1)
        norm2 = normalize(real_func2, args2, kwargs2)
    except TypeError:
        # If they can’t even be bound, consider them unequal
        return False

    # 3) Compare final argument mappings
    return norm1 == norm2

def get_active_pids() -> Set[int]:
    """
    获取系统活动PID
    一般不使用该函数,其会检测所有并创建Process对象,造成资源浪费
    如果需要判断对应进程是否存在,请使用psutil.pid_exists(pid)
    """
    active_pids = set()
    for proc in psutil.process_iter(attrs=['status']):
        status = proc.info.get('status')  # type: ignore
        # 过滤僵尸进程
        if status and status != psutil.STATUS_ZOMBIE:
            active_pids.add(proc.pid)

    return active_pids

def update_config(file_path, section, config_dict):
    """
    更新配置文件中的指定节点内容

    参数:
        file_path: 配置文件路径
        section: 节点名称
        config_dict: 要更新/添加的配置项字典 {配置项: 值}
    """
    config = configparser.ConfigParser(interpolation=None)
    # 保留原文件大小写（默认转换为小写）
    config.optionxform = lambda option: option
    file_path = get_real_path_create_dir(file_path,path_is_dir=False)
    # 如果文件存在则读取
    if os.path.exists(file_path):
        config.read(file_path, encoding='utf-8')

    # 如果节点不存在则创建
    if not config.has_section(section):
        config.add_section(section)

    # 更新/添加所有配置项
    for key, value in config_dict.items():
        config.set(section, key, str(value))

    # 写入文件（自动创建新文件）
    with open(file_path, 'w', encoding='utf-8') as configfile:
        config.write(configfile) # type: ignore

def synch_section(file_path, section, config_dict):
    """
    同步配置文件中的指定节点内容，该配置文件中的其他节点的内容不变

    参数:
        file_path: 配置文件路径
        section: 节点名称
        config_dict: 需要完全同步的配置项字典 {配置项: 值}
    """
    config = configparser.ConfigParser(interpolation=None)
    # 保留原文件大小写（默认转换为小写）
    config.optionxform = lambda option: option
    file_path = get_real_path_create_dir(file_path, path_is_dir=False)

    # 如果文件存在则读取
    if os.path.exists(file_path):
        config.read(file_path, encoding='utf-8')

    # 确保 section 存在
    if not config.has_section(section):
        config.add_section(section)

    # 清空原 section 的所有项
    for key in list(config[section].keys()):
        config.remove_option(section, key)

    # 添加新的配置项
    for key, value in config_dict.items():
        config.set(section, key, str(value))

    # 写入文件（保留其他 section）
    with open(file_path, 'w', encoding='utf-8') as f:
        config.write(f) # type: ignore



def format_time_monotonic_interval(start_timestamp):
    """
    计算两个monotonic时间戳间隔，并格式化为 "xxx天xx:xx:xx"

    参数:
        start_timestamp (float): 输入时间戳（time.monotonic()格式） 起始时间

    返回:
        str: 格式化的时间间隔字符串
    """
    # 1. 获取当前时间戳
    current_timestamp = time.monotonic()

    # 2. 计算时间差（秒）
    time_diff = abs(current_timestamp - start_timestamp)

    # 3. 分解时间差为天、小时、分钟、秒
    days = int(time_diff // (24 * 3600))
    remaining_seconds = time_diff % (24 * 3600)

    hours = int(remaining_seconds // 3600)
    remaining_seconds %= 3600

    minutes = int(remaining_seconds // 60)
    seconds = int(remaining_seconds % 60)

    # 4. 格式化为字符串 (xx:xx:xx 固定2位)
    return time_diff,f"{days}天 {hours:02d}:{minutes:02d}:{seconds:02d}"


def format_time_for_display(time_str: str) -> str:
    """
    将字符串时间转换成前端gui显示格式,如:1小时前,5分钟前;
    :param time_str: 字符串时间,格式为'%Y-%m-%d %H:%M:%S'
    由于`time_str`是从数据库中获取到的,其值已经经过时区转换,所以这里不需要再转换
    """
    if not time_str:
        return "未知"
    past = datetime.strptime(time_str, common_constants.DATETIME_FORMAT)
    now = datetime.now()
    delta = now - past

    # 1 分钟内：秒
    seconds = int(delta.total_seconds())
    if seconds < 60:
        return f"{seconds}秒前"

    # 1 小时内：分钟
    minutes = seconds // 60
    if minutes < 60:
        return f"{minutes}分钟前"

    # 1 天内：小时
    hours = minutes // 60
    if hours < 24:
        return f"{hours}小时前"

    # 1 个月内：天（这里按30天计算）
    days = hours // 24
    if days < 30:
        return f"{days}天前"

    # 1 年内：月（按年月差计算，更准确）
    # 先计算年月总差值
    year_diff = now.year - past.year
    month_diff = now.month - past.month
    total_months = year_diff * 12 + month_diff
    if total_months < 12:
        return f"{total_months}月前"

    # 超过 1 年：年
    # 如果当前月-日还没到过 past 的月-日，则实足年数要减 1
    years = year_diff
    if (now.month, now.day) < (past.month, past.day):
        years -= 1
    return f"{years}年前"

def is_pyinstaller_bundle() -> bool:
    """检测是否在 PyInstaller 打包环境中运行"""
    return getattr(sys, 'frozen', False) and hasattr(sys, '_MEIPASS')



def monitor_independent_process_startup(independent_process:subprocess.Popen)->int|None:
    """
    等待确认指定进程是否启动成功
    返回值: None 表示启动成功 其他值表示 该进程退出码
    """
    logger_print(msg="waiting for independent_process startup confirmation", custom_logger=logger)
    max_wait_time = 4  # 最大等待时间（秒）
    check_interval = 0.2  # 检查间隔（秒）
    elapsed_time = 0

    while elapsed_time < max_wait_time:
        # 检查进程是否还在运行
        poll_result = independent_process.poll()
        # 0表示正常退出，非0表示异常退出
        if poll_result is not None and poll_result!= 0:
            # 进程已结束，启动失败
            logger_print(msg=f"independent_process process exited with code: {poll_result}", custom_logger=logger)

            # 尝试读取进程输出以获取更多错误信息
            process_end_output(independent_process)


            return poll_result

        time.sleep(check_interval)
        elapsed_time += check_interval

    return None

def process_end_output(independent_process: subprocess.Popen):
    """对应进程在结束之后打印其输出"""
    if independent_process is None or independent_process.poll() is None:
        return
    try:
        if hasattr(independent_process, 'stdout') and independent_process.stdout:
            stdout_output = independent_process.stdout.read()
            if stdout_output:
                logger_print(msg=f"process stdout: {stdout_output}", custom_logger=logger)
        if hasattr(independent_process, 'stderr') and independent_process.stderr:
            stderr_output = independent_process.stderr.read()
            if stderr_output:
                logger_print(msg=f"process stderr: {stderr_output}", custom_logger=logger)
    except Exception: # noqa
        pass

def remove_firewall_rule_with_windows(rule_name:str):
    """在Windows环境下删除指定防火墙规则"""
    if not common_constants.IS_WINDOWS:
        return
    
    cmd = [
        "netsh", "advfirewall", "firewall", "delete", "rule",
        f"name={rule_name}"
    ]
    result = subprocess.run(cmd, capture_output=True, text=True, shell=True)
    if result.returncode != 0:
        logger_print(msg=f"删除指定防火墙规则失败: {rule_name}, {result.stderr}",custom_logger=logger,log_level=logging.ERROR)

def restart_cur_program():
    """重启当前程序"""
    # 执行清除操作
    shutdown_exec_funct.execute_registered_functions()
    executable = sys.executable
    args=sys.argv if is_pyinstaller_bundle() else [executable]+sys.argv
    logger_print(msg=f"restarting current program with args: {args}", custom_logger=logger)
    if common_constants.IS_WINDOWS:
        subprocess.Popen(args, cwd=os.getcwd(),creationflags=subprocess.DETACHED_PROCESS | subprocess.CREATE_NEW_PROCESS_GROUP)
    else:
        os.execv(executable, args)
    sys.exit(0)


def check_runtime_allowed(run_time_str: str, current_time_zone: ZoneInfo) -> Tuple[bool, bool, datetime.time, datetime.time, datetime.time]:
    """
    检查当前时间是否在允许的运行时间段内
    
    Args:
        run_time_str: 运行时间段字符串，格式为 "HH:MM-HH:MM"，如 "07:00-19:00"
        current_time_zone: 当前时区
        
    Returns:
        Tuple[bool, bool, datetime.time, datetime.time, datetime.time]: 
        (can_run, always_run, start_time, end_time, now)
        - can_run: 是否可以运行
        - always_run: 是否一直运行（开始时间和结束时间相同）
        - start_time: 开始时间
        - end_time: 结束时间  
        - now: 当前时间
    """
    start_time, end_time = run_time_str.split('-')
    start_time = datetime.strptime(start_time.strip(), "%H:%M").time()
    end_time = datetime.strptime(end_time.strip(), "%H:%M").time()
    now = datetime.now(current_time_zone).time()
    
    # 开始时间和结束时间一致,则一直运行
    always_run = (start_time == end_time)
    
    # 支持跨天运行
    can_run = (always_run or (start_time <= now <= end_time)
               or (end_time < start_time and (now <= end_time or now >= start_time)))
    
    return can_run, always_run, start_time, end_time, now

def get_miss_key_in_dict(dict_obj: dict[str, Any], required_keys: set[str]) -> set[str]:
    """
    获取相对于required_keys来说，字典中缺少的键,包含dict中value为None的键也视为缺少
    :param dict_obj:
    :param required_keys:
    """
    missing_keys = set()
    for key in required_keys:
        if key not in dict_obj or dict_obj[key] is None:
            missing_keys.add(key)
    return missing_keys
