"""Webhook服务器命令行启动模块。

此模块提供了Webhook服务器的命令行启动功能，支持通过命令行参数
指定配置文件路径来启动服务器。
"""

import argparse
import asyncio
import logging
import sys
from pathlib import Path
from typing import Optional

from common.utils import path_manager  # 确保路径设置
from webhook_server.models import webhook_server
from common.utils import server_utils

if __name__ == "__main__":
    # 初始化临时logger用于命令行启动日志
    temp_logger: Optional[logging.Logger] = logging.getLogger(__name__)
    server_utils.logger_print(
        msg="webhook server command line startup", custom_logger=temp_logger
    )
    parser = argparse.ArgumentParser(description="Webhook Server Command Line Interface")
    parser.add_argument(
        "--config", required=True, help="path to server config file"
    )
    args = parser.parse_args()
    server_utils.logger_print(
        msg=f"parsed command line arguments: config={args.config}",
        custom_logger=temp_logger
    )
    asyncio.run(webhook_server.run_server_with_path(args.config))
