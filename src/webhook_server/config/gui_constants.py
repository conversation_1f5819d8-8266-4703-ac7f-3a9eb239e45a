"""GUI界面中涉及到的常量值定义模块。

此模块包含了GUI界面相关的所有常量，包括：
- 软件基础信息
- 窗口尺寸和布局配置
- 颜色和主题设置
- 默认配置值
- 日志配置
"""

import os
import re

from common.constants import common_constants

# 软件基础信息
VERSION = "1.0.1"
#和spec文件中设置的软件名称保持一致
SPEC_SOFTWARE_NAME = 'NexusRecv'
SOFTWARE_NAME = "多类别设备数据接收存储服务端"
CONTACT_INFORMATION = "<EMAIL>"
RELEASE_TIME = "2025-06-01"
INSTRUCTION_SOFTWARE_USE = (
    "该软件可以用于接收并存储多种类型设备的实时数据并分发给指定的第三方使用。"
)
LICENSE_AGREEMENT = (
    "本软件遵循Apache License 2.0开源协议发布,"
    "用户应遵守相关法律法规和协议使用条款。"
)

# gui配置项显示的默认值
SERVER_INITIAL_STARTUP_TIME_DEFAULT_VAR = '服务端初始启动时间: ----'
SERVER_PID_DEFAULT_VAR = '服务端PID: ------'
SERVER_MEMORY_DEFAULT_VAR = '内存使用: 00.00 MB'
# 在主界面gui中显示的消息长度,其实际存储的内容不变
SERVER_GUI_MESSAGE_SHOW_CHAR_LENGTH = 43

# 用户界面窗口长宽设置
SERVER_CONFIG_DIALOG_SIZE = (590, 200)
CLIENT_INFO_DIALOG_SIZE = (600, 280)
CLIENT_NEW_DIALOG_SIZE = (400, 150)
WEBHOOK_SERVER_GUI_SIZE = (850, 580)
# 绿/红 RGB颜色值
GREEN_COLOR = (22, 198, 12)
RED_COLOR = (232, 18, 36)
# 在配置加载界面中每页显示的配置项数量
CONFIG_PAGE_NUM = 10
LOADING_CONFIGURATION_MESSAGE = "正在加载配置..."
# 'cosmo', 'flatly', 'litera', 'minty', 'lumen', 'sandstone', 'yeti', 'pulse', 'united', 'morph', 'journal', 'darkly', 'superhero', 'solar', 'cyborg', 'vapor', 'simplex', 'cerculean'
DEFAULT_THEME="flatly"

# 软件图标路径 - 只需要这一个图标
SMALL_ICON_PATH = common_constants.get_resource_path("./resources/22.png")
# 配置加载界面中不同类型的标签值
VALID_TAG = 'valid'
OCCUPIED_TAG = 'occupied'
INVALID_TAG = 'invalid'
# 配置加载界面中行提示前缀
ROW_TIP_PREFIX = "当前行的配置文件路径: "
# 软件主界面上通知栏的默认消息
DEFAULT_NOTIFICATION_MESSAGE = (
    "欢迎使用多类别设备数据接收存储服务端,"
    "在服务端运行时存在数据的情况下,双击实时数据中表格中指定行可复制完整数据内容"
)
# 软件主界面上通知栏的默认消息颜色
# ---------------------- 配置文件新建时的配置项默认值 ------------------------
WHITELIST = "*"
SERVER_CONFIG_DIR = os.path.join(os.path.expanduser("~"), ".webhook_server")
ERROR_SERVER_GUI_DIR=os.path.join(SERVER_CONFIG_DIR,"error_server_gui")
APP_NAME_PREFIX = "服务端配置_"
MESSAGE_DATA_TABLE_NAME_PREFIX = "message_data_"
# 服务器配置文件名正则匹配:新建的gui界面的配置文件名称的正则表达式
SERVER_CONFIG_FILENAME_PREFIX = "server_config_"
SERVER_CONFIG_FILENAME_SUFFIX = ".ini"
SERVER_CONFIG_FILENAME_PATTERN = re.compile(
    rf'{re.escape(SERVER_CONFIG_FILENAME_PREFIX)}(\d+)'
    rf'{re.escape(SERVER_CONFIG_FILENAME_SUFFIX)}'
)
LOG_CONFIG_PATH = os.path.join(
    os.path.expanduser("~"), ".webhook_server", "log_config.ini"
)
SERVER_CONFIG_PATH = os.path.join(
    os.path.expanduser("~"), ".webhook_server", "server_config.ini"
)
HOST = "0.0.0.0"
PORT = 8000
RUN_TIME = "07:00-07:00"
EXPIRE_DATA_DAYS = 3
DATA_LIMIT_NUM = 100000
ENABLE_SQL_LOGGING = True

# -----------常量定义--------------
# cpu仪表盘阈值不同的颜色样式
CPU_THRESHOLD_SEGMENT_STYLE = {10: "success", 30: "warning", 100: "danger"}

# 配置文件中唯一性配置项,在多进程之间唯一,在新建/加载配置时需要进行校验
SERVER_CONFIG_UNIQUE_KEYS = {"port", "message_data_table_name", "app_name"}

# 用户可视化界面中可自定义的服务端配置项,在加载配置时不需要进行校验
USER_CUSTOM_KEYS = {"api_key", "run_time"}

# 以下是log相关的配置 DEBUG
LOG_DIR = os.path.join(os.path.expanduser("~"), ".webhook_server", "logs")
LOG_FILE_NAME_PREFIX = "gui_webhook_server"
