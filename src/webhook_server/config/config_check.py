"""GUI界面配置项校验以及默认项的配置模块。

此模块提供了配置文件的校验功能，包括：
- 配置文件格式和编码校验
- 配置项有效性检查
- 默认配置生成
- 配置文件管理
"""

import configparser
import logging
import os
import sqlite3
from pathlib import Path
from typing import Any

from common.constants import common_constants
from common.utils import server_utils, self_log
from webhook_server.config import constants, gui_constants
from webhook_server.utils import config_lock, webhook_server_utils

logger = logging.getLogger(__name__)

def common_check_config_file(server_config_file_path: str) -> None:
    """校验配置文件是否有效-常见和公共的校验逻辑,无效则抛出异常。

    Args:
        server_config_file_path: 服务器配置文件路径

    Raises:
        ValueError: 当配置文件无效时抛出异常

    校验内容:
        1. 配置文件是否存在且ini格式且非空
        2. 配置文件是否可读
        3. 配置文件是否UTF-8编码
    """
    if not os.path.exists(server_config_file_path):
        server_utils.logger_print(
            f"server config path:{server_config_file_path} not exist",
            custom_logger=logger, log_level=logging.ERROR
        )
        raise ValueError(f"配置文件{server_config_file_path}不存在!")
    if not os.path.isfile(server_config_file_path):
        server_utils.logger_print(
            f"server config path:{server_config_file_path} not exist or not a file",
            custom_logger=logger, log_level=logging.ERROR
        )
        raise ValueError(f"配置文件{server_config_file_path}不存在或不是文件!")
    if not server_config_file_path.endswith(gui_constants.SERVER_CONFIG_FILENAME_SUFFIX):
        server_utils.logger_print(
            f"server config path:{server_config_file_path} not end with "
            f"{gui_constants.SERVER_CONFIG_FILENAME_SUFFIX}",
            custom_logger=logger, log_level=logging.ERROR
        )
        raise ValueError(
            f"配置文件{server_config_file_path}后缀名不是"
            f"{gui_constants.SERVER_CONFIG_FILENAME_SUFFIX}!"
        )
    if os.path.getsize(server_config_file_path) <= 0:
        server_utils.logger_print(
            f"server config path:{server_config_file_path} is empty",
            custom_logger=logger, log_level=logging.ERROR
        )
        raise ValueError(f"配置文件{server_config_file_path}为空!")
    if not os.access(server_config_file_path, os.R_OK):
        server_utils.logger_print(
            f"server config path:{server_config_file_path} not have read permission",
            custom_logger=logger, log_level=logging.ERROR
        )
        raise ValueError(f"配置文件{server_config_file_path}没有读权限,无法读取配置项!")
    if not server_utils.is_utf8_encoding_file(server_config_file_path):
        server_utils.logger_print(
            f"server config path:{server_config_file_path} not utf-8 encoding",
            custom_logger=logger, log_level=logging.ERROR
        )
        raise ValueError(f"配置文件{server_config_file_path}不是UTF-8编码,可能读取出错!")

def _get_server_dir_filename_nums(server_config_dir_path: Path) -> set[int]:
    """获取在服务端配置文件目录中所有服务端配置文件的数字编号。

    Args:
        server_config_dir_path: 服务端配置目录路径

    Returns:
        set[int]: 配置文件的数字编号集合
    """
    res = set()
    for f in server_config_dir_path.iterdir():
        if f.is_file():
            match = gui_constants.SERVER_CONFIG_FILENAME_PATTERN.fullmatch(f.name)
            if match:
                res.add(int(match.group(1)))
    return res


def _get_db_filename_nums() -> set[int]:
    """获取在数据库记录中中对应服务端数据库文件的数字编号。

    Returns:
        set[int]: 数据库中记录的配置文件数字编号集合
    """
    res = set()
    # 可能包含不匹配SERVER_CONFIG_FILENAME_PATTERN的文件名,所以需要过滤掉
    config_manager = config_lock.MultiProcessConfigManager.get_singleton_instance()
    for f_p in config_manager.get_all_server_config_paths_in_db():
        match = gui_constants.SERVER_CONFIG_FILENAME_PATTERN.fullmatch(f_p)
        if match:
            res.add(int(match.group(1)))
    return res

def check_config_section_key_value_format_valid(section: str, key: str, value: str) -> None:
    """校验配置文件中的所有范围内的指定节点的配置项是否格式正确。

    Args:
        section: 配置节名称
        key: 配置项键名
        value: 配置项值

    Raises:
        ValueError: 当配置项格式不正确时抛出异常给前端显示
    """
    if server_utils.trim(section) is None:
        server_utils.logger_print(
            f"section:{section} is not a valid string!",
            custom_logger=logger, log_level=logging.WARNING
        )
        raise ValueError(f"section:{section} is not a valid string!")
    if server_utils.trim(key) is None:
        server_utils.logger_print(
            f"config section[{section}] key:{key} is not a valid string!",
            custom_logger=logger, log_level=logging.WARNING
        )
        raise ValueError(f"config section[{section}] key:{key} is not a valid string!")
    if server_utils.trim(value) is None:
        server_utils.logger_print(
            f"config section[{section}] key:{key} value:{value} is not a valid string!",
            custom_logger=logger, log_level=logging.WARNING
        )
        raise ValueError(f"config section[{section}] key:{key} value:{value} is not a valid string!")
    if section == 'client_info':
        webhook_server_utils.check_one_client_info(key, value)
    elif section == 'server':
        if key=='api_key':
            if not constants.SERVER_API_KEY_PATTERN.fullmatch(value):
                raise ValueError(constants.ERROR_SERVER_API_KEY_MSG)
        elif key=='whitelist':
            try:
                server_utils.get_whitelist(value)
            except ValueError:
                server_utils.logger_print(f"server properties [server]-[whitelist] value:{value} format error!", custom_logger=logger, use_exception=True)
                raise ValueError(constants.ERROR_WHITELIST_MSG)
        elif key=='message_data_table_name':
            if not constants.MESSAGE_DATA_TABLE_NAME_PATTERN.fullmatch(value):
                raise ValueError(constants.ERROR_MESSAGE_DATA_TABLE_NAME_MSG)
        elif key=='log_config_path':
            log_config_path_str = server_utils.get_real_path(value)
            if not os.path.isfile(log_config_path_str) or os.path.getsize(log_config_path_str) <= 0:
                raise ValueError(constants.ERROR_LOG_CONFIG_PATH_MSG)
        elif key=='host':
            try:
                server_utils.check_lan_host(value)
            except ValueError:
                raise ValueError(constants.ERROR_HOST_MSG)
        elif key=='port':
            try:
                server_utils.get_server_config_port(value)
            except ValueError:
                server_utils.logger_print(f"server properties [server]-[port] value:{value} format error!", custom_logger=logger, use_exception=True)
                raise ValueError(constants.ERROR_PORT_MSG)
        elif key=='run_time':
            if not constants.SERVER_RUN_TIME_PATTERN.fullmatch(value):
                raise ValueError(constants.ERROR_RUNTIME_MSG)
        elif key=='time_zone':
            try:
                server_utils.check_time_zone_str(value)
            except ValueError:
                server_utils.logger_print(f"server properties [server]-[time_zone] value:{value} format error!", custom_logger=logger, use_exception=True)
                raise ValueError(constants.ERROR_TIME_ZONE_MSG)
        elif key=='expire_data_days':
            try:
                int(value)
            except ValueError:
                server_utils.logger_print(f"server properties [server]-[expire_data_days] value:{value} format error!", custom_logger=logger, use_exception=True)
                raise ValueError(constants.ERROR_EXPIRE_DATA_DAYS_MSG)
        elif key=='data_limit_num':
            try:
                int(value)
            except ValueError:
                server_utils.logger_print(f"server properties [server]-[data_limit_num] value:{value} format error!", custom_logger=logger, use_exception=True)
                raise ValueError(constants.ERROR_DATA_LIMIT_NUM_MSG)
        elif key=='enable_sql_logging':
            try:
               assert server_utils.str_to_bool(value) is not None
            except (ValueError,AssertionError):
                server_utils.logger_print(f"server properties [server]-[enable_sql_logging] value:{value} format error!", custom_logger=logger, use_exception=True)
                raise ValueError(constants.ERROR_ENABLE_SQL_LOGGING_MSG)
        elif key!='app_name':
            # 由于 `app_name`的值校验只需要判断对应字符串是否为空,这已经进行了校验,所以不需要再次校验
            # 之后如果新增字段的校验也仅仅只是字符串非空校验,那么和'app_name'一样放到这里处理
            server_utils.logger_print(f"unknown server config key:{key} in section:{section}", custom_logger=logger, log_level=logging.WARNING)
            raise ValueError(f"配置文件[server]节点的{key}配置项没有配置检测逻辑,请联系开发者!")
def _server_config_entries_format_valid(server_config_entries:dict[str, Any]):
    """
    校验服务端配置文件[server]节点的配置项格式是否正确：
        不是用户界面自定义的配置项,其值必须存在且格式正确
        是用户界面自定义的配置项,其值可以为空,但如果存在则必须格式正确
    抛出的异常给前端显示
    """
    if server_config_entries is None or not isinstance(server_config_entries, dict):
        raise ValueError(constants.ERROR_SERVER_CONFIG_MISS_SERVER_MSG)
    for key, value in server_config_entries.items():
        need_check=key not in gui_constants.USER_CUSTOM_KEYS or value is not None
        if need_check:
            check_config_section_key_value_format_valid("server", key, value)
    log_config_path = server_config_entries.get("log_config_path")
    if log_config_path is not None:
        log_config_path_str = server_utils.get_real_path(log_config_path)
        server_config_entries["log_config_path"] = log_config_path_str
    expire_data_days = server_config_entries.get("expire_data_days")
    if expire_data_days is not None :
         server_config_entries["expire_data_days"] = int(expire_data_days)
    data_limit_num = server_config_entries.get("data_limit_num")
    if data_limit_num is not None:
         server_config_entries["data_limit_num"] = int(data_limit_num)

def _client_info_values_key_unique(client_info_values:dict[str, Any]):
    """
    校验客户端信息配置文件[client_info]节点的配置项是否具有唯一性：
        由于其已经在其他代码处进行了格式校验,所以不需要在这里再次校验,直接进行key的唯一性校验
        客户端信息配置文件[client_info]节点的配置项必须具有唯一性,不能重复
        抛出的异常给前端显示
    """
    if client_info_values is None:
        return
    if not isinstance(client_info_values, dict):
        raise ValueError("配置文件中[client_info]节点解析错误!")
    all_len=len(client_info_values)
    if all_len<2:
        return
    client_info_keys=set(client_info_values.keys())
    if len(client_info_keys)!=all_len:
        raise ValueError(constants.ERROR_CLIENT_INFO_KEY_UNIQUE_MSG)

def create_default_log_config_file(server_config_dir_path:Path)->str:
    """创建gui界面默认的日志配置文件"""
    log_config_path = server_config_dir_path / "log.ini"
    if not log_config_path.exists():
        log_write_config = configparser.ConfigParser(interpolation=None)
        log_write_config["log"] = {
            "level": str(common_constants.LOG_LEVEL),
            "console": str(common_constants.LOG_CONSOLE),
            "file": str(common_constants.LOG_FILE),
            "dir": server_utils.get_real_path_create_dir(path=gui_constants.LOG_DIR, path_is_dir=True),
            "filename_prefix": str(gui_constants.LOG_FILE_NAME_PREFIX),
            "filename_date_fmt": str(common_constants.LOG_FILE_DATE_FMT),
            "filename": str(common_constants.LOG_FILENAME),
            "max_size": str(common_constants.LOG_FILE_MAX_SIZE),
            "backup_count": str(common_constants.LOG_BACKUP_COUNT),
            "format": str(common_constants.LOG_FORMAT),
            "date_fmt": str(common_constants.LOG_DATE_FMT),
            "expire_logs_days": str(common_constants.LOG_EXPIRE_LOGS_DAYS),
            "zone": str(common_constants.LOG_ZONE)
        }
        log_write_config["colors"]={
            "debug": str(common_constants.LOG_COLOR_DEBUG),
            "info": str(common_constants.LOG_COLOR_INFO),
            "warning": str(common_constants.LOG_COLOR_WARNING),
            "error": str(common_constants.LOG_COLOR_ERROR),
            "critical": str(common_constants.LOG_COLOR_CRITICAL)
        }
        with open(log_config_path, 'w', encoding='utf-8') as write_file:
            log_write_config.write(write_file)# type: ignore
    return str(log_config_path)

#
def create_new_config_file()->str:
    """"
     新建配置时自动生成对应的配置文件 --- 不包含gui自定义配置项  USER_CUSTOM_KEYS
     该函数内包含数据库操作,不能被(cur: sqlite3.Cursor)函数内调用
    """
    server_config_dir_path = server_utils.get_real_path_create_dir(path=gui_constants.SERVER_CONFIG_DIR, path_is_dir=True)
    dir_path = Path(server_config_dir_path)
    config_filename_nums = _get_server_dir_filename_nums(dir_path)
    config_filename_nums.update(_get_db_filename_nums())
    cur_config_filename_num = max(config_filename_nums, default=0)+1
    cur_config_filename=f"{gui_constants.SERVER_CONFIG_FILENAME_PREFIX}{cur_config_filename_num}{gui_constants.SERVER_CONFIG_FILENAME_SUFFIX}"
    cur_config_file_path = dir_path / cur_config_filename
    valid_config_ports=config_lock.MultiProcessConfigManager.get_singleton_instance().get_valid_values_for_key("port")
    if not valid_config_ports:
        cur_port=gui_constants.PORT
    else:
        cur_port=max(int(p) for p in valid_config_ports)+1
    # 如果日志配置文件不存在则新增创建
    config = configparser.ConfigParser(interpolation=None)
    # 其中不小于包含gui自定义配置项的配置项:USER_CUSTOM_KEYS
    config["server"] = {
        "whitelist": str(gui_constants.WHITELIST),
        "message_data_table_name":f"{gui_constants.MESSAGE_DATA_TABLE_NAME_PREFIX}{cur_config_filename_num}",
        "log_config_path": create_default_log_config_file(dir_path),
        "host": str(gui_constants.HOST), # 显示指定某个明确的IP地址则会被限定访问这个地址
        "port": str(cur_port),
        "time_zone": str(common_constants.TIME_ZONE),
        "expire_data_days": str(gui_constants.EXPIRE_DATA_DAYS),
        "data_limit_num": str(gui_constants.DATA_LIMIT_NUM),
        "app_name": f"{gui_constants.APP_NAME_PREFIX}{cur_config_filename_num}",
        "enable_sql_logging":str(gui_constants.ENABLE_SQL_LOGGING)
    }

    with open(cur_config_file_path, 'w', encoding='utf-8') as write_file:
        config.write(write_file)# type: ignore

    return str(cur_config_file_path)

def check_new_get_config_file(server_config_file_path:str,cur: sqlite3.Cursor)->dict[str, Any]:
    """
    校验[新建或者手动选择]配置文件内是否符合配置文件规范: 其抛出的异常是给前端显示的
    新建或者手动选择配置文件时：
        1. 数据库中不存在该记录
        2. 文件实际存在且ini格式且可读且UTF-8编码
        3. 必要的配置项不能缺失 [除去gui自定义配置项]
        4. 已有的配置项对应的配置项格式正确
        5. 具有唯一性的配置项不能重复
    """

    server_config_file_path = server_utils.get_real_path(path=server_config_file_path)
    common_check_config_file(server_config_file_path)
    if config_lock.MultiProcessConfigManager.check_config_path_in_db(server_config_file_path, cur):
        server_utils.logger_print(f"server config path:{server_config_file_path} already exist in db, please choose another one", custom_logger=logger, log_level=logging.ERROR)
        raise ValueError(f"服务端配置文件{server_config_file_path}已经存在于数据库中，请选择其他配置文件!")
    try:
        server_config = configparser.ConfigParser(interpolation=None)
        # key保持原本的大小写
        server_config.optionxform = str
        server_config.read(server_config_file_path, encoding="utf-8")
        server_config_entries = server_utils.section_to_dict(server_config, "server", True)
        missing = constants.SERVER_REQUIRED_KEYS - gui_constants.USER_CUSTOM_KEYS - server_config_entries.keys()
        if missing:
            server_utils.logger_print(f"server config path:{server_config_file_path} missing required keys:{missing}", custom_logger=logger, log_level=logging.ERROR)
            raise ValueError(f"服务端配置文件{server_config_file_path}缺少必要的配置项:{missing},服务端配置文件不完整!")
    except Exception: # noqa
        server_utils.logger_print(f"server config path:{server_config_file_path} parse error!", custom_logger=logger, use_exception=True)
        raise
    _server_config_entries_format_valid(server_config_entries)
    _client_info_values_key_unique(server_utils.section_to_dict(server_config, "client_info"))
    # 除去用户界面自定义的配置项,其他已经定义的唯一配置项的值不能重复
    for unique_key in gui_constants.SERVER_CONFIG_UNIQUE_KEYS:
        cur_value = server_config_entries.get(unique_key)
        if (cur_value is not None or unique_key not in gui_constants.USER_CUSTOM_KEYS) and config_lock.MultiProcessConfigManager.check_value_exist_in_db(unique_key, cur_value, cur):
            raise ValueError(f"服务端配置文件{server_config_file_path}的唯一性配置项{unique_key}的值:{cur_value}已经存在于数据库中,为保证唯一性,请修改该值!")

    return server_config_entries

def check_loading_gui_list_config_file(server_config_file_path:str,checksum:str):
    """
    校验需要[加载到配置界面的,选择有效的加载到程序的]的配置文件是否符合规范[排除正在运行webhook所使用的配置文件]:
    调用该函数前.对应的参数必须实时从数据库中获取最新的数据,其必须是有效的配置文件
    如果是重新加载到配置界面报错时,在数据库操作那边将该配置记录设置是无效并添加失效原因
    如果是选择有效的加载到程序中报错时,则直接将报错信息提供给前端界面并刷新列表
    返回值: 0: 配置失效, 1: 配置有效, 2: 配置文件被其他进程占用[不可能,占用的进程会自动更新数据库记录,而本函数获取到的是有效的但没有被进程占用的配置文件]
    可能0:失效的情况一般是raise异常 可能2不会出现,所以这里不需要返回值
    校验逻辑:
    1. 配置记录在数据库中[其本身就从数据库中获取,所以就不需要再次校验]
    2. 该配置文件实际存在,ini格式,UTF-8编码,可读
    3. 配置未被其他进程占用[本数据来源就是未被占用且有效的配置文件,所以不用再次校验]
    4. 实际哈希值和数据库中记录的哈希值一致
    5. 配置项完整且格式正确[由于和数据库哈希值一致,那么其配置项必定完整且格式正确]
    6. 唯一配置项不重复[只能是在新增和修改时存在冲突的情况,而本函数使用在加载配置列表和重新加载配置列表的情况,所以不需要校验]
    """
    # 由于本函数的server_config_file_path来自数据库记录,该值一定是在新增时填充的,而这时必定是全完整路径,所以不需要再次获取全路径
    common_check_config_file(server_config_file_path)
    if checksum != server_utils.file_hash(server_config_file_path):
        raise ValueError(f"服务端配置文件{server_config_file_path}在非程序控制下被修改,该配置文件失效,如果需要使用该配置文件请清除失效文件后重新手动加载该配置文件!")
def check_log_config_file_to_runtime_webhook(log_config_file_path:str):
    """
    校验要给webhook server运行的log配置文件内是否符合要求:
    该值从服务端配置文件'log_config_path'配置项值获取,可能是相对路径,所以需要先获取全路径
    一般和webhook运行前的服务端配置文件校验一起执行
    1. 配置文件实际存在,ini格式,UTF-8编码,可读
    2. 配置项完整且格式正确[由于是给webhook运行的配置文件,所以其配置项必定完整且格式正确]
    """
    log_config_file_path = server_utils.get_real_path(path=log_config_file_path)
    common_check_config_file(log_config_file_path)
    try:
        log_config = configparser.ConfigParser(interpolation=None)
        log_config.read(log_config_file_path, encoding="utf-8")
        self_log.LogConfig.get_log_config(log_config)
    except Exception: # noqa
        server_utils.logger_print(f"log config path:{log_config_file_path} parse error!", custom_logger=logger, use_exception=True)
        raise ValueError(f"日志配置文件{log_config_file_path}解析错误!")

def check_server_config_file_to_runtime_webhook(server_config_file_path:str):
    """
    校验要给webhook server运行的配置文件内是否符合要求：
    对应的数据来源是从数据库中获取,路径是全路径,所以不需要再次获取全路径
    抛出异常则说明该配置文件失效,不能用于webhook运行
    1. 配置文件实际存在,ini格式,UTF-8编码,可读
    2. 所以的配置项完整且格式正确 --- [server]节点和[client_info]节点必须存在且格式正确
    """
    common_check_config_file(server_config_file_path)
    try:
        server_config = configparser.ConfigParser(interpolation=None)
        # key保持原本的大小写
        server_config.optionxform = str
        server_config.read(server_config_file_path, encoding="utf-8")
        server_config_entries = server_utils.section_to_dict(server_config, "server", True)
        missing = server_utils.get_miss_key_in_dict(dict_obj=server_config_entries, required_keys=constants.SERVER_REQUIRED_KEYS)
        if missing:
            server_utils.logger_print(f"server config path:{server_config_file_path} missing required keys:{missing}", custom_logger=logger, log_level=logging.ERROR)
            raise ValueError(f"服务端配置文件{server_config_file_path}缺少必要的配置项:{missing},服务端配置文件不完整!")
        log_config_file_path=server_config_entries["log_config_path"]
    except Exception: # noqa
        server_utils.logger_print(f"server config path:{server_config_file_path} parse error!", custom_logger=logger, use_exception=True)
        raise
    # [server]节点必须存在且格式正确
    _server_config_entries_format_valid(server_config_entries)
    # [client_info]节点必须存在且格式正确
    try:
        client_info_properties = server_utils.section_to_dict(server_config, "client_info")
        webhook_server_utils.check_all_client_info_config(client_info_properties)
        _client_info_values_key_unique(client_info_properties)
    except Exception: # noqa
        server_utils.logger_print(f"server config path:{server_config_file_path} client_info parse error!", custom_logger=logger, use_exception=True)
        raise ValueError(f"服务端配置{server_config_file_path}的发信设备标识信息不能为空!")
    check_log_config_file_to_runtime_webhook(log_config_file_path)


