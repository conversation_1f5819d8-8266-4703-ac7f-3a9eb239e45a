# 不 实用的或者过时的代码
import ttkbootstrap as ttkb
from ttkbootstrap.constants import *
from ttkbootstrap.style import Bootstyle

from common.utils import server_utils

"""折叠面板:会导致其他控件的位置变化"""
class CollapsingFrame(ttkb.Frame):
    """A collapsible frame widget that opens and closes with a click."""

    def __init__(self, master,shrinking_path, expanding_path, **kwargs):
        shrinking_path= server_utils.get_real_exist_file_path(shrinking_path)
        expanding_path= server_utils.get_real_exist_file_path(expanding_path)
        super().__init__(master, **kwargs)
        self.columnconfigure(0, weight=1)
        self.cumulative_rows = 0

        # widget images
        self.images = [
            ttkb.PhotoImage(file=shrinking_path),
            ttkb.PhotoImage(file=expanding_path)
        ]

    def add(self, child, title="", bootstyle=PRIMARY, **kwargs):
        """Add a child to the collapsible frame

        Parameters:

            child (Frame):
                The child frame to add to the widget.

            title (str):
                The title appearing on the collapsible section header.

            bootstyle (str):
                The style to apply to the collapsible section header.

            **kwargs (Dict):
                Other optional keyword arguments.
        """
        if child.winfo_class() != 'TFrame':
            return

        style_color = Bootstyle.ttkstyle_widget_color(bootstyle)
        frm = ttkb.Frame(self, bootstyle=style_color) # noqa
        frm.grid(row=self.cumulative_rows, column=0, sticky=EW)

        # header title
        header = ttkb.Label(
            master=frm,
            text=title,
            bootstyle=(style_color, INVERSE)# noqa
        )
        if kwargs.get('textvariable'):
            header.configure(textvariable=kwargs.get('textvariable'))
        header.pack(side=LEFT, fill=BOTH, padx=10)

        # header toggle button
        def _func(c=child): return self._toggle_open_close(c)
        btn = ttkb.Button(
            master=frm,
            image=self.images[0],
            bootstyle=style_color, # noqa
            command=_func
        )
        btn.pack(side=RIGHT)

        # assign toggle button to child so that it can be toggled
        child.btn = btn
        child.grid(row=self.cumulative_rows + 1, column=0, sticky=NSEW)
        child.grid_remove()

        # increment the row assignment
        self.cumulative_rows += 2

    def _toggle_open_close(self, child):
        """Open or close the section and change the toggle button
        image accordingly.

        Parameters:

            child (Frame):
                The child element to add or remove from grid manager.
        """
        if child.winfo_viewable():
            child.grid_remove()
            child.btn.configure(image=self.images[1])
        else:
            child.grid()
            child.btn.configure(image=self.images[0])
