import asyncio
import configparser
import logging
import subprocess
import sys
import threading
import time
from uuid import uuid4

import pytest
import requests
from models import webhook_server

from common.utils import server_utils, shutdown_exec_funct

logger = logging.getLogger(__name__)
@pytest.fixture(scope="function")
def temp_config(tmp_path):
    """生成临时配置文件并返回创建函数"""
    def _create_config(port=8000, api_key: str = "testKey123456", message_data_table_name=None) -> str:
        # 设置默认路径
        if not message_data_table_name:
            message_data_table_name = str(uuid4())
        log_config_path = "D:/Git/python-samples-hub/resources/log.ini"
        

        # 创建配置
        config = configparser.ConfigParser()
        config["server"] = {
            "api_key": api_key,
            "whitelist": "*",
            "message_data_table_name": message_data_table_name,
            "log_config_path": log_config_path,
            "host": "0.0.0.0", # 显示指定某个明确的IP地址则会被限定访问这个地址
            "port": str(port),
            "run_time": "00:00-00:00",  # 全天运行
            "time_zone": "Asia/Shanghai",
            "expire_data_days": "5",
            "data_limit_num": "1000",
            "app_name": str(uuid4())
        }
        config["client_info"] = {"testClientKey": "测试设备"}

        config_path = tmp_path / f"server_config_{port}.ini"
        with open(config_path, 'w', encoding='utf-8') as write_file:
            config.write(write_file)# type: ignore
        return str(config_path)

    return _create_config


async def async_task(webhook_server_instance, config_path):
    """异步运行webhook服务器"""
    webhook_server_instance.web_server_state.properties = webhook_server.server_properties.ServerProperties(config_path)
    webhook_server_instance.logger = logging.getLogger(__name__)
    shutdown_exec_funct.register(webhook_server_instance.cleanup_on_shutdown)
    await webhook_server_instance.run_server()


def thread_target(webhook_server_instance, config_path):
    """线程目标函数，运行webhook服务器"""
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        loop.run_until_complete(async_task(webhook_server_instance, config_path))
    finally:
        loop.close()


def start_server_in_thread(config_path):
    """在新线程中启动服务器并返回服务器实例和线程"""
    server = webhook_server.WebhookServer()
    server_thread = threading.Thread(target=thread_target, args=(server, config_path), daemon=True)
    server_thread.start()
    time.sleep(2)  # 等待服务器启动
    return server, server_thread


def start_server_in_process(config_path):
    """在新进程中启动服务器"""
    cmd = [sys.executable, "-m", "webhook_server.models.webhook_server", "--config", config_path]
    # PIPE缓存通道满导致进程阻塞,直接不使用PIPE,直接在主进程中输出日志
    process = subprocess.Popen(cmd)
    print(f"process id: {process.pid}")
    time.sleep(5)  # 等待服务器启动
    return process


def is_port_in_use(port):
    """检查端口是否被占用:一直被拒绝，放弃该方法"""
    time.sleep(5)  # 等待进程启动和端口绑定
    return server_utils.is_port_occupied(port)


def cleanup_server(server):
    """清理服务器资源"""
    if server and hasattr(server, 'web_server_state'):
        if hasattr(server.web_server_state, 'end_flag'):
            server.web_server_state.end_flag.set()
        server.cleanup_on_shutdown()


# ---------------------- 多实例测试 ----------------------
def test_same_config_same_process(temp_config):
    """测试在同一进程中使用相同配置文件启动多个实例"""
    config_path = temp_config(8001)

    server1, thread1 = start_server_in_thread(config_path)
    time.sleep(2)
    
    # 确认第一个实例成功启动
    assert is_port_in_use(8001), "第一个服务器实例未能成功启动"
    
    # 尝试启动第二个实例（应该失败）
    server2 = webhook_server.WebhookServer() # noqa
    thread2 = threading.Thread(target=thread_target, args=(server2, config_path), daemon=True)
    thread2.start()
    time.sleep(2)
    assert not thread2.is_alive(), "第二个服务器实例应该启动失败"
    # 清理资源
    cleanup_server(server1)
    cleanup_server(server2)
    thread1.join(timeout=2)
    thread2.join(timeout=2)


def test_different_config_same_process(temp_config):
    """测试在同一进程中使用不同配置文件启动多个实例:失效,不论是相同配置还是不同配置都是失效,进行了同一个进程配置项的限制"""
    config1 = temp_config(8002)
    config2 = temp_config(8003)
    
    # 启动两个实例
    server1, thread1 = start_server_in_thread(config1)
    time.sleep(2)
    server2, thread2 = start_server_in_thread(config2)
    time.sleep(2)
    
    # 验证两个实例都成功启动
    assert is_port_in_use(8002), "第一个服务器实例未能成功启动"
    assert not is_port_in_use(8003), "第二个服务器实例应该启动失败"
    
    # 验证实例隔离性
    # assert server1.web_server_state.properties.server_config["port"] != server2.web_server_state.properties.server_config["port"]
    # assert server1.web_server_state.server_refresh_token != server2.web_server_state.server_refresh_token

    # 清理资源
    cleanup_server(server1)
    cleanup_server(server2)
    thread1.join(timeout=2)
    thread2.join(timeout=2)


def test_same_config_different_process(temp_config):
    """测试在不同进程中使用相同配置文件启动实例"""
    config_path = temp_config(8004)
    processes = []
    p1=None
    try:
        # 启动第一个进程
        p1 = start_server_in_process(config_path)
        time.sleep(5)
        print(f"p1.pid:{p1.pid}")

        # 确认第一个进程成功启动
        assert is_port_in_use(8004), "第一个服务器进程未能成功启动"
        assert p1.poll() is None, "第一个进程应该仍然运行"
        # 尝试启动第二个进程或者更多（应该失败）
        for i in range(10):
            process2 = start_server_in_process(config_path)
            processes.append(process2)
            assert process2.poll() is not None, "第二个进程应该已经退出"
    finally:
        # 清理资源
        print(111)
        if p1:
            p1.terminate()
            p1.wait()
        for process in processes:
            if process:
                process.terminate()
                process.wait()


def test_different_config_different_process(temp_config):
    """测试在不同进程中使用不同配置文件启动实例"""
    config1 = temp_config(8005)
    config2 = temp_config(8006)
    
    # 启动两个进程
    process1 = start_server_in_process(config1)
    process2 = start_server_in_process(config2)
    time.sleep(2)
    
    # 验证两个进程都成功启动
    assert is_port_in_use(8005), "第一个服务器进程未能成功启动"
    assert is_port_in_use(8006), "第二个服务器进程未能成功启动"
    
    # 清理资源
    process1.terminate()
    process2.terminate()
    process1.wait()
    process2.wait()


# ---------------------- 配置项校验测试 ----------------------
def test_missing_required_config(tmp_path):
    """测试必要配置项缺失时的错误处理"""
    # 创建缺少必要配置项的配置文件
    config = configparser.ConfigParser()
    config["server"] = {
        # 缺少 api_key
        "whitelist": "*",
        "host": "127.0.0.1",
        "port": "8007"
    }
    config_path = tmp_path / "missing_config.ini"
    with open(config_path, 'w', encoding='utf-8') as f:
        config.write(f) # type: ignore
    
    # 尝试启动服务器
    server = webhook_server.WebhookServer()
    with pytest.raises(Exception):
        asyncio.run(async_task(server, str(config_path)))


def test_invalid_config_format(temp_config):
    """测试配置项格式错误时的校验"""
    # 创建API密钥长度不足的配置
    config_path = temp_config(8008, api_key="short")
    
    # 尝试启动服务器
    server = webhook_server.WebhookServer()
    with pytest.raises(Exception):
        asyncio.run(async_task(server, config_path))


def test_port_conflict(temp_config):
    """测试端口冲突时的处理"""
    # 创建两个使用相同端口的配置
    config1 = temp_config(8009)
    config2 = temp_config(8009)  # 相同端口
    
    # 启动第一个服务器
    server1, thread1 = start_server_in_thread(config1)
    time.sleep(2)
    
    # 尝试启动第二个服务器（应该失败）
    server2 = webhook_server.WebhookServer()
    with pytest.raises(Exception):
        asyncio.run(async_task(server2, config2))

    # 清理资源
    cleanup_server(server1)
    thread1.join(timeout=2)


def test_data_path_conflict(temp_config, tmp_path):
    """测试数据存储表冲突时的处理"""
    # 创建共享同一数据表的两个配置
    shared_data_table_name = "shared_data"
    config1 = temp_config(8010, message_data_table_name=shared_data_table_name)
    config2 = temp_config(8011, message_data_table_name=shared_data_table_name)
    
    # 启动第一个服务器
    p1=start_server_in_process(config1)
    time.sleep(2)
    # 尝试启动第二个服务器（应该失败）
    p2=start_server_in_process(config2)
    time.sleep(2)

    assert is_port_in_use(8010), "第一个服务器进程未能成功启动"
    assert not is_port_in_use(8011), "第二个服务器进程未能成功启动"

    # 清理资源
    p1.terminate()
    p2.terminate()
    p1.wait()
    p2.wait()


# ---------------------- API功能测试 ----------------------
def test_webhook_api(temp_config):
    """测试webhook接口功能"""
    config_path = temp_config(8012)
    api_key="testKey123456"
    client_id="testclientkey"
    # 启动服务器
    p1=start_server_in_process(config_path)
    time.sleep(2)
    
    try:
        # 获取token
        token_url = "http://127.0.0.1:8012/webhook/token"

        headers = {'Authorization': f'Bearer {api_key}'}
        token_response = requests.get(token_url, headers=headers)
        assert token_response.status_code == 200, "获取token失败"
        token_data = token_response.json()
        assert "token" in token_data, "响应中没有token字段"
        token = token_data["token"]
        
        # 发送消息
        save_url = "http://127.0.0.1:8012/webhook/save"
        headers = {
            'x-client-key': client_id,
            'Content-Type': 'application/json'  # 必须声明
        }
        save_data = {"content": "测试消息"}
        save_response = requests.post(
            save_url, 
            json=save_data, 
            headers=headers
        )
        assert save_response.status_code == 200, "发送消息失败"
        save_result = save_response.json()
        assert save_result["status"] == "success", "消息发送状态不是success"
        
        # 读取未读消息
        unread_url = "http://127.0.0.1:8012/webhook/unread"
        headers = {"Authorization": f"Bearer {token}"}
        params={'size':10}
        unread_response = requests.get(
            unread_url, 
            headers=headers,
            params=params
        )
        assert unread_response.status_code == 200, "读取未读消息失败"
        unread_data = unread_response.json()['messages']
        assert len(unread_data) > 0, "未读消息列表为空"
        assert unread_data[0]["message"] == "测试消息", "消息内容不匹配"
    finally:
        # 清理资源
        p1.terminate()
        p1.wait()


def test_whitelist_restriction(temp_config):
    """测试白名单限制功能"""
    # 创建仅允许特定IP的配置
    config = configparser.ConfigParser()
    config["server"] = {
        "api_key": "testKey123456",
        "whitelist": "***********",  # 仅允许此IP
        "message_data_table_name": "test_data",
        "log_config_path": "test_log.ini",
        "host": "127.0.0.1",
        "port": "8013",
        "run_time": "00:00-00:00",
        "time_zone": "Asia/Shanghai",
        "expire_data_days": "5",
        "data_limit_num": "1000"
    }
    config["client_info"] = {"test_client_key": "测试设备"}
    
    config_path = temp_config(8013)
    with open(config_path, 'r', encoding='utf-8') as f:
        config_orig = configparser.ConfigParser()
        config_orig.read_file(f)
    
    config_orig["server"]["whitelist"] = "***********"
    with open(config_path, 'w', encoding='utf-8') as f:
        config_orig.write(f) # type: ignore
    
    # 启动服务器
    server, thread = start_server_in_thread(config_path)
    time.sleep(2)
    api_key = "testKey123456"
    try:
        # 尝试获取token（应该失败，因为本地请求IP不在白名单中）
        token_url = "http://127.0.0.1:8013/webhook/token"
        headers = {'Authorization': f'Bearer {api_key}'}
        token_response = requests.get(token_url, headers=headers)
        assert token_response.status_code == 403, "白名单限制未生效"
    finally:
        # 清理资源
        cleanup_server(server)
        thread.join(timeout=2)


def test_api_key_validation(temp_config):
    """测试API密钥验证"""
    config_path = temp_config(8014)
    
    # 启动服务器
    server, thread = start_server_in_thread(config_path)
    time.sleep(2)
    api_key = "testKey123456-1"
    try:
        # 使用错误的API密钥尝试获取token
        token_url = "http://127.0.0.1:8014/webhook/token"
        headers = {'Authorization': f'Bearer {api_key}'}
        token_response = requests.get(token_url, headers=headers)
        assert token_response.status_code == 403, "API密钥验证未生效"
        api_key="testKey123456"
        headers = {'Authorization': f'Bearer {api_key}'}
        # 使用正确的API密钥获取token
        valid_response = requests.get(token_url, headers=headers)
        assert valid_response.status_code == 200, "使用正确的API密钥成功"
    finally:
        # 清理资源
        cleanup_server(server)
        thread.join(timeout=2)


def test_complete_data_flow(temp_config):
    """测试完整的数据发送和接收流程"""
    config_path = temp_config(8015)
    
    # 启动服务器
    server, thread = start_server_in_thread(config_path)
    time.sleep(2)
    api_key = "testKey123456"
    client_id = "testclientkey"
    try:
        # 获取token
        token_url = "http://127.0.0.1:8015/webhook/token"
        headers = {'Authorization': f'Bearer {api_key}'}
        token_response = requests.get(token_url, headers=headers)
        token = token_response.json()["token"]
        
        # 发送多条消息
        save_url = "http://127.0.0.1:8015/webhook/save"
        headers = {
            'x-client-key': client_id,
            'Content-Type': 'application/json'  # 必须声明
        }
        messages = ["消息1", "消息2", "消息3"]
        
        for msg in messages:
            save_response = requests.post(
                save_url, 
                json={"content": msg}, 
                headers=headers
            )
            assert save_response.status_code == 200
        
        # 读取未读消息
        unread_url = "http://127.0.0.1:8015/webhook/unread"
        headers = {"Authorization": f"Bearer {token}"}
        params={'size':10}
        unread_response = requests.get(
            unread_url, 
            headers=headers,
            params=params
        )
        unread_data = unread_response.json()['messages']
        
        # 验证消息数量和内容
        assert len(unread_data) == 3, "未读消息数量不正确"
        received_messages = [msg["message"] for msg in unread_data]
        for msg in messages:
            assert msg in received_messages, f"消息 '{msg}' 未被接收"
        
        # 再次读取未读消息（应该为空，因为已经被标记为已读）
        unread_response = requests.get(
            unread_url, 
            headers=headers,
            params=params
        )
        unread_data = unread_response.json()['messages']
        assert len(unread_data) == 0, "消息未被正确标记为已读"
    finally:
        # 清理资源
        cleanup_server(server)
        thread.join(timeout=2)

if __name__ == '__main__':
    pytest.main(["-s", __file__])
