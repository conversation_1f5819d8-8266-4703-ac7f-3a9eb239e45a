import logging
import os
import unittest
from unittest.mock import patch

from apscheduler.schedulers.background import BackgroundScheduler

from config import constants
from src.models.server_properties import ServerConfiguration
from common.utils import server_utils

logger = logging.getLogger(__name__)

class TestServerConfiguration(unittest.TestCase):
    def setUp(self):
        self.valid_config = os.path.abspath('D:\\Git\python-samples-hub\\tests\\other_conf\\server_config.ini')
        print(self.valid_config)
        self.invalid_config = os.path.abspath('D:\\Git\python-samples-hub\\tests\\other_conf\\invalid_config.ini')

    def tearDown(self):
        super().tearDown()
        print("tearDown")


    # 测试_check_set__config方法
    def test_check_set_config(self):
        from configparser import ConfigParser
        config = ConfigParser()
        config['server'] = {' host': ' *********** ', 'port ': ' 8080 '}

        result = server_utils.section_to_dict(config, 'server')
        self.assertEqual(result['host'], '***********')
        self.assertEqual(result['port'], '8080')
        config['server'] = {' ': ' *********** ', 'port ': ' 8080 '}
        with self.assertRaises(ValueError):
            server_utils.section_to_dict(config, 'server')
        config['server'] = {'host ': ' *********** ', 'port ': '  '}
        with self.assertRaises(ValueError):
            server_utils.section_to_dict(config, 'server')
        config['server'] = {'host ': ' *********** ', ' host ': '  '}
        with self.assertRaises(ValueError):
            server_utils.section_to_dict(config, 'server')
            
    # 测试_check_reset_server_config方法
    def test_check_reset_server_config(self):
        valid_config = {
            'host': '***********',
            'port': '8080',
            'whitelist': '***********/24,********',
            'time_zone': 'Asia/Shanghai',
            'run_time': '08:00-20:00',
            'api_key': 'secure_key_12345',
            'message_data_table_name': 'messages',
            'log_config_path': '../resources/log.ini',
            'expire_data_days': '30',
            'data_limit_num': '100000',
        }

        ServerConfiguration.validate_and_convert_config(valid_config)
        self.assertIsInstance(valid_config['port'], int)
        self.assertIsInstance(valid_config['whitelist'], list)

    def test_invalid_config(self):
        # server properties [server] missing required keys
        invalid_config = {
            'host': '***********',
            'whitelist': '***********',
            'time_zone': 'Asia/Shanghai',
            'run_time': '08:00-20:00',
            'api_key': 'secure_key_12345',
            'message_data_table_name': 'data',
            'log_config_path': '../resources/log.ini',
            'expire_data_days': '30',
            'data_limit_num': '100000',
        }
        with self.assertRaises(ValueError) as context:
            ServerConfiguration.validate_and_convert_config(invalid_config)
        self.assertIn('missing required keys', str(context.exception))
        invalid_config = {
            'host': '***********',
            'port': '70000',  # 无效端口
            'whitelist': '***********',
            'time_zone': 'Asia/Shanghai',
            'run_time': '08:00-20:00',
            'api_key': 'secure_key_12345',
            'message_data_table_name': 'data',
            'log_config_path': '../resources/log.ini',
            'expire_data_days': '30',
            'data_limit_num': '100000',
        }

        with self.assertRaises(ValueError) as context:
            ServerProperties.check_reset_server_config(invalid_config)
        self.assertIn('must be between 1 and 65535', str(context.exception))
        invalid_config['port'] = '0'
        with self.assertRaises(ValueError) as context:
            ServerProperties.check_reset_server_config(invalid_config)
        self.assertIn('must be between 1 and 65535', str(context.exception))
        invalid_config['port'] = ''
        with self.assertRaises(ValueError) as context:
            ServerProperties.check_reset_server_config(invalid_config)
        self.assertIn('must be a valid integer', str(context.exception))

    # 测试完整初始化流程
    @patch('os.path.isfile')
    def test_full_initialization(self, mock_isfile):
        mock_isfile.return_value = True
        server_props = ServerConfiguration(self.valid_config, BackgroundScheduler(timezone=constants.DEFAULT_TIMEZONE))
        self.assertEqual(server_props.server_config['host'], '***********')
        self.assertIsInstance(server_props.server_config['port'], int)
        print("xxxxxxxxx")
        logger.info("xxxxxxxxxxxxxxx")

    # 测试无效配置文件异常
    def test_missing_section(self):
        with self.assertRaises(ValueError) as context:
            ServerConfiguration('D:\\Git\python-samples-hub\\tests\\other_conf\\missing_section.ini',
                             BackgroundScheduler(timezone=constants.DEFAULT_TIMEZONE))
        self.assertIn('missing [server] section', str(context.exception))


if __name__ == '__main__':
    unittest.main()
