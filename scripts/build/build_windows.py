#!/usr/bin/env python3
"""
Windows平台PyInstaller打包脚本
"""
import sys

from build_common import (
    SOFTWARE_NAME, DIST_DIR, main_build_flow,
    copy_file_with_info, get_file_size_mb, print_success_message, print_failure_message
)
from common.utils import server_utils


def cleanup_firewall_rules():
    """清理Windows防火墙规则"""
    try:
        server_utils.remove_firewall_rule_with_windows(SOFTWARE_NAME)
        print("✓ 旧防火墙规则清理完成")
    except ImportError:
        print("警告: 无法导入server_utils模块，跳过防火墙规则清理")
    except Exception as e:
        print(f"警告: 防火墙规则清理失败: {e}")


def handle_windows_build_output():
    """
    处理Windows构建输出
    :return: 是否成功
    """
    # 检查输出
    exe_path = DIST_DIR / f"{SOFTWARE_NAME}.exe"
    dir_path = DIST_DIR / SOFTWARE_NAME

    if exe_path.exists():
        # 单文件模式
        print(f"✓ 单文件打包成功: {exe_path}")
        print(f"文件大小: {get_file_size_mb(exe_path):.2f} MB")
        copy_onefile_to_release()

    elif dir_path.exists():
        # 目录模式
        print(f"✓ 目录打包成功: {dir_path}")
        copy_ondir_to_release()

    else:
        print("错误: 未找到输出文件")
        return False

    return True


def main():
    """Windows打包主函数"""
    return main_build_flow(
        platform_name="Windows",
        additional_cleanup=cleanup_firewall_rules,
        post_build_handler=handle_windows_build_output
    )

def copy_onefile_to_release():
    """
    复制单文件到发布目录
    """
    from build_common import create_release_dir

    software_file = f"{SOFTWARE_NAME}.exe"
    onefile_out_exe = DIST_DIR/ software_file

    # 创建发布目录
    release_dir = create_release_dir( "windows")

    # 复制文件
    copy_file_with_info(onefile_out_exe, release_dir / software_file, "发布文件")

    # 创建启动脚本
    create_launcher_script(release_dir, software_file)

def copy_ondir_to_release():
    """
    复制单目录到发布目录
    """
    from build_common import create_release_dir, copy_tree_with_info

    # 创建发布目录
    release_dir = create_release_dir( "windows")

    gen_out_software_dir = DIST_DIR / SOFTWARE_NAME

    # 复制目录
    copy_tree_with_info(gen_out_software_dir, release_dir / SOFTWARE_NAME, "发布目录")

    # 创建启动脚本
    create_launcher_script(release_dir, f"{SOFTWARE_NAME}.exe", is_dir=True)

def create_launcher_script(release_dir, exe_name, is_dir=False):
    """创建启动脚本"""
    launcher_script = release_dir / "start.bat"
    if is_dir:
        # 目录模式的启动脚本
        software_name = exe_name.replace('.exe', '')
        run_exe_relative_path =f"{software_name}\\{exe_name}"
    else:
        # 单文件模式的启动脚本
        run_exe_relative_path = exe_name

    script_content = f'''
    @echo off
    chcp 65001 >nul
    echo 启动NexusRecv WebHook Server GUI...
    echo.
    echo 如果遇到问题，请检查：& echo 1. 防火墙设置 & echo 2. 端口占用情况 & echo 3. 配置文件权限
    echo.
    cd /d "%~dp0"
    {run_exe_relative_path}
    pause
    '''

    with open(launcher_script, 'w', encoding='utf-8') as f:  # 使用 GBK 编码
        f.write(script_content)

    print(f"✓ 启动脚本: {launcher_script}")

if __name__ == "__main__":
    success = main()
    if success:
        print_success_message("Windows")
        sys.exit(0)
    else:
        print_failure_message("Windows")
        sys.exit(1)
    # create_launcher_script(create_release_dir( "windows"), f"{SOFTWARE_NAME}.exe", is_dir=True)
